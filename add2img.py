from fastmcp import FastMCP
from PIL import Image
import cv2
from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware
import requests
import numpy as np
import os
import tempfile
import importlib.util
import glob
import sys

# 配置 CORS middleware
cors_middleware = [
    Middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 在生产环境中应该指定具体的域名
        allow_credentials=True,
        allow_methods=["GET", "POST", "OPTIONS"],
        allow_headers=["*"],
    ),
]

mcp = FastMCP("Demo 🚀")

# 1. 获取tools文件夹下的所有py文件
def get_py_files_from_tools():
    """获取tools文件夹下的所有Python文件"""
    tools_dir = "tools"
    if not os.path.exists(tools_dir):
        return []
    
    py_files = glob.glob(os.path.join(tools_dir, "*.py"))
    return py_files

# 2. 导入所有py_list内的文件的函数
def import_functions_from_files(py_list):
    """从Python文件列表中导入所有函数"""
    func_list = []
    
    for py_file in py_list:
        try:
            # 获取模块名（不包含路径和扩展名）
            module_name = os.path.splitext(os.path.basename(py_file))[0]
            
            # 尝试读取文件内容以确定编码
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
            except UnicodeDecodeError:
                # 如果UTF-8解码失败，尝试使用GBK编码
                print(f"UTF-8解码失败，尝试使用GBK编码读取: {py_file}")
                with open(py_file, 'r', encoding='gbk') as f:
                    content = f.read()
                # 将GBK编码的内容重新保存为UTF-8
                with open(py_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"已将文件 {py_file} 转换为UTF-8编码")
            
            # 动态导入模块
            spec = importlib.util.spec_from_file_location(module_name, py_file)
            module = importlib.util.module_from_spec(spec)
            
            # 记录导入前的函数列表
            sys.modules[module_name] = module
            spec.loader.exec_module(module)
            
            # 获取模块中的所有函数
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if callable(attr) and not attr_name.startswith('_'):
                    # 检查是否是函数且不是内置函数
                    if hasattr(attr, '__call__') and not attr_name in ['Image']:
                        # 更宽松的检查条件
                        if hasattr(attr, '__name__') and attr.__name__ == attr_name:
                            func_list.append(attr)
                            print(f"找到函数: {attr_name} 来自 {py_file}")
                        
        except Exception as e:
            print(f"导入文件 {py_file} 时出错: {e}")
            
    return func_list

# 3. 把func_list的所有函数包装为mcp_tool
def wrap_functions_as_mcp_tools(func_list):
    """将函数列表包装为MCP工具"""
    for func in func_list:
        try:
            # 使用装饰器将函数注册为MCP工具
            mcp.tool(func)
            print(f"已注册MCP工具: {func.__name__}")
        except Exception as e:
            print(f"注册工具 {func.__name__} 时出错: {e}")

# 初始化：加载tools文件夹中的所有工具
py_list = get_py_files_from_tools()
func_list = import_functions_from_files(py_list)
wrap_functions_as_mcp_tools(func_list)

@mcp.tool
def add2img(img_path1:str, img_path2:str) -> str:
    """
    将两张图片进行混合
    Args:
        img_path1: 第一张图片的路径,可以是网络路径
        img_path2: 第二张图片的路径,可以是网络路径
    Returns:
        混合后的图片在文件服务器上的URL
    """
    # 下载图片
    print(img_path1, img_path2)
    img1_pil = Image.open(requests.get(img_path1, stream=True).raw)
    img2_pil = Image.open(requests.get(img_path2, stream=True).raw)
    
    # 将PIL图片转换为cv2格式 (RGB -> BGR)
    img1 = cv2.cvtColor(np.array(img1_pil), cv2.COLOR_RGB2BGR)
    img2 = cv2.cvtColor(np.array(img2_pil), cv2.COLOR_RGB2BGR)
    
    # 将两张图片都调整到640*640
    shape1 = img1.shape  # HWC
    shape2 = img2.shape  # HWC
    max1 = max(shape1[0], shape1[1])
    max2 = max(shape2[0], shape2[1])
    img1 = cv2.copyMakeBorder(
        src=img1,
        top=int((max1 - shape1[0])/2),
        bottom=int((max1 - shape1[0])/2),
        left=int((max1 - shape1[1])/2),
        right=int((max1 - shape1[1])/2),
        borderType=cv2.BORDER_REFLECT101,
    )
    img1 = cv2.resize(src=img1, dsize=(640, 640), interpolation=cv2.INTER_LINEAR)
    img2 = cv2.copyMakeBorder(
        src=img2,
        top=int((max2 - shape2[0])/2),
        bottom=int((max2 - shape2[0])/2),
        left=int((max2 - shape2[1])/2),
        right=int((max2 - shape2[1])/2),
        borderType=cv2.BORDER_REFLECT101,
    )
    img2 = cv2.resize(src=img2, dsize=(640, 640), interpolation=cv2.INTER_LINEAR)

    # 按照比例将两张图片进行混合
    alpha = 0.5
    beta = 1.0 - alpha
    img_blending = cv2.addWeighted(src1=img1, alpha=alpha, src2=img2, beta=beta, gamma=.0)
    
    # 将cv2图片转换为PIL格式 (BGR -> RGB)
    img_blending_pil = Image.fromarray(cv2.cvtColor(img_blending, cv2.COLOR_BGR2RGB))
    
    # 创建临时文件保存图片
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
        temp_filename = temp_file.name
        img_blending_pil.save(temp_filename, format='PNG')
    
    try:
        # 上传到文件服务器
        with open(temp_filename, 'rb') as f:
            files = {'file': ('blended_image.png', f, 'image/png')}
            response = requests.post('http://localhost:4000/upload', files=files)
            
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                return result['file']['downloadUrl']
            else:
                return f"上传失败: {result.get('message', '未知错误')}"
        else:
            return f"上传失败: HTTP {response.status_code}"
            
    except Exception as e:
        return f"上传失败: {str(e)}"
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_filename):
            os.unlink(temp_filename)

if __name__ == "__main__":
    # 创建带有 CORS middleware 的 HTTP 应用，启用 stateless 模式
    http_app = mcp.http_app(
        middleware=cors_middleware,
        transport='streamable-http',
        stateless_http=True  # 启用 stateless 模式
    )
    

    # 使用 uvicorn 运行应用
    import uvicorn
    uvicorn.run(http_app, host="127.0.0.1", port=1111)