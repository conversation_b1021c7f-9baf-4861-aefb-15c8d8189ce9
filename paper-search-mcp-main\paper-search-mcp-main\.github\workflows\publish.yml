name: Publish to PyPI

on:
  push:
    tags:
      - 'v*.*.*'  

jobs:
  publish:
    runs-on: ubuntu-latest
    environment: pypi  
    permissions:
      id-token: write  
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'

      - name: Install build tools
        run: |
          python -m pip install --upgrade pip
          pip install build

      - name: Build package
        run: python -m build

      - name: Publish to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1