@echo off
echo ===================================================
echo MCP Agent Web Service - cURL 测试脚本
echo ===================================================
echo.

echo 1. 测试健康检查
echo ---------------------------------------------------
curl.exe -X GET "http://localhost:8001/health" -H "Content-Type: application/json"
echo.
echo.

echo 2. 获取示例请求格式
echo ---------------------------------------------------
curl.exe -X GET "http://localhost:8001/example" -H "Content-Type: application/json"
echo.
echo.

echo 3. 测试根路径
echo ---------------------------------------------------
curl.exe -X GET "http://localhost:8001/" -H "Content-Type: application/json"
echo.
echo.

echo 4. 测试非流式请求 - 简单数学计算
echo ---------------------------------------------------
curl.exe -X POST "http://localhost:8001/agent/run" ^
  -H "Content-Type: application/json" ^
  -d "{\"model_name\": \"qwen3:1.7b\", \"mcp_urls\": [\"http://localhost:11435/mcp\"], \"instructions\": \"Use the tools to answer the questions.\", \"message\": \"Add 5 and 3\", \"stream\": false, \"temperature\": 0.0, \"max_tokens\": 500}"
echo.
echo.

echo 5. 测试非流式请求 - 复杂数学计算
echo ---------------------------------------------------
curl.exe -X POST "http://localhost:8001/agent/run" ^
  -H "Content-Type: application/json" ^
  -d "{\"model_name\": \"qwen3:1.7b\", \"mcp_urls\": [\"http://localhost:11435/mcp\"], \"instructions\": \"Use the tools to answer the questions.\", \"message\": \"what is (36 + 51) x 123899?\", \"stream\": false, \"temperature\": 0.0, \"max_tokens\": 1000}"
echo.
echo.

echo 6. 测试非流式请求 - 文本处理
echo ---------------------------------------------------
curl.exe -X POST "http://localhost:8001/agent/run" ^
  -H "Content-Type: application/json" ^
  -d "{\"model_name\": \"qwen3:1.7b\", \"mcp_urls\": [\"http://localhost:11435/mcp\"], \"instructions\": \"You are a helpful assistant.\", \"message\": \"Hello, how are you?\", \"stream\": false, \"temperature\": 0.1, \"max_tokens\": 200}"
echo.
echo.

echo 7. 测试流式请求 - 数学计算
echo ---------------------------------------------------
echo 注意：流式请求会持续输出，按 Ctrl+C 可以停止
timeout /t 3 /nobreak > nul
curl.exe -X POST "http://localhost:8001/agent/stream" ^
  -H "Content-Type: application/json" ^
  -d "{\"model_name\": \"qwen3:1.7b\", \"mcp_urls\": [\"http://localhost:8000/mcp\"], \"instructions\": \"Use the tools to answer the questions.\", \"message\": \"what is 2 + 2?\", \"stream\": true, \"temperature\": 0.0, \"max_tokens\": 500}"
echo.
echo.

echo 8. 测试流式请求 - 带参数调整
echo ---------------------------------------------------
echo 使用更高的温度和更多token进行测试
timeout /t 3 /nobreak > nul
curl.exe -X POST "http://localhost:8001/agent/stream" ^
  -H "Content-Type: application/json" ^
  -d "{\"model_name\": \"qwen3:1.7b\", \"mcp_urls\": [\"http://localhost:11435/mcp\"], \"instructions\": \"Use the tools to answer the questions. Be creative and detailed.\", \"message\": \"Tell me a short story about a robot\", \"stream\": true, \"temperature\": 0.3, \"max_tokens\": 800, \"top_p\": 0.9}"
echo.
echo.

echo 9. 测试列表类型消息 - 非流式
echo ---------------------------------------------------
echo 测试包含上下文的对话消息格式
curl.exe -X POST "http://localhost:8001/agent/run" ^
  -H "Content-Type: application/json" ^
  -d "{\"model_name\": \"qwen3:1.7b\", \"mcp_urls\": [\"http://localhost:11435/mcp\"], \"instructions\": \"Use the tools to answer the questions.\", \"message\": [{\"role\": \"system\", \"content\": \"You are a helpful assistant.\"}, {\"role\": \"user\", \"content\": \"Calculate 15 * 7\"}, {\"role\": \"assistant\", \"content\": \"I'll help you calculate that.\"}, {\"role\": \"user\", \"content\": \"Please show the steps.\"}], \"stream\": false, \"temperature\": 0.0, \"max_tokens\": 500}"
echo.
echo.

echo 10. 测试列表类型消息 - 流式
echo ---------------------------------------------------
echo 测试流式输出的对话消息格式
timeout /t 3 /nobreak > nul
curl.exe -X POST "http://localhost:8001/agent/stream" ^
  -H "Content-Type: application/json" ^
  -d "{\"model_name\": \"qwen3:1.7b\", \"mcp_urls\": [\"http://localhost:11435/mcp\"], \"instructions\": \"Use the tools to answer the questions.\", \"message\": [{\"role\": \"system\", \"content\": \"You are a helpful math tutor.\"}, {\"role\": \"user\", \"content\": \"What is 25 + 17?\"}, {\"role\": \"assistant\", \"content\": \"I can help you with that calculation.\"}, {\"role\": \"user\", \"content\": \"Please explain each step.\"}], \"stream\": true, \"temperature\": 0.1, \"max_tokens\": 600}"
echo.
echo.

echo 11. 测试列表类型消息 - 多轮对话场景（非流式）
echo ---------------------------------------------------
echo 测试包含复杂对话历史的消息格式
curl.exe -X POST "http://localhost:8001/agent/run" ^
  -H "Content-Type: application/json" ^
  -d "{\"model_name\": \"qwen3:1.7b\", \"mcp_urls\": [\"http://localhost:11435/mcp\"], \"instructions\": \"You are a coding assistant. Help users with programming questions.\", \"message\": [{\"role\": \"system\", \"content\": \"You are an expert Python programmer.\"}, {\"role\": \"user\", \"content\": \"How do I create a list in Python?\"}, {\"role\": \"assistant\", \"content\": \"You can create a list using square brackets: my_list = [1, 2, 3]\"}, {\"role\": \"user\", \"content\": \"What about adding elements to it?\"}, {\"role\": \"assistant\", \"content\": \"You can use append() method: my_list.append(4)\"}, {\"role\": \"user\", \"content\": \"Can you show me a complete example?\"}], \"stream\": false, \"temperature\": 0.2, \"max_tokens\": 800}"
echo.
echo.

echo 12. 测试列表类型消息 - 科学计算场景（流式）
echo ---------------------------------------------------
echo 测试科学计算相关的对话消息格式
timeout /t 3 /nobreak > nul
curl.exe -X POST "http://localhost:8001/agent/stream" ^
  -H "Content-Type: application/json" ^
  -d "{\"model_name\": \"qwen3:1.7b\", \"mcp_urls\": [\"http://localhost:11435/mcp\"], \"instructions\": \"Use the tools for calculations. Show detailed steps.\", \"message\": [{\"role\": \"system\", \"content\": \"You are a scientific calculator assistant.\"}, {\"role\": \"user\", \"content\": \"I need to calculate the area of a circle.\"}, {\"role\": \"assistant\", \"content\": \"I can help you calculate the area. The formula is π × r²\"}, {\"role\": \"user\", \"content\": \"The radius is 5 units. Please calculate it step by step.\"}], \"stream\": true, \"temperature\": 0.0, \"max_tokens\": 700}"
echo.
echo.

echo 13. 测试列表类型消息 - 创意写作场景（非流式）
echo ---------------------------------------------------
echo 测试创意写作相关的对话消息格式
curl.exe -X POST "http://localhost:8001/agent/run" ^
  -H "Content-Type: application/json" ^
  -d "{\"model_name\": \"qwen3:1.7b\", \"mcp_urls\": [\"http://localhost:11435/mcp\"], \"instructions\": \"You are a creative writing assistant.\", \"message\": [{\"role\": \"system\", \"content\": \"You are a professional creative writer.\"}, {\"role\": \"user\", \"content\": \"I want to write a story about time travel.\"}, {\"role\": \"assistant\", \"content\": \"That's an interesting topic! Time travel stories offer many possibilities.\"}, {\"role\": \"user\", \"content\": \"What if someone travels back 100 years?\"}, {\"role\": \"assistant\", \"content\": \"Going back 100 years would mean arriving in the 1920s - a fascinating era!\"}, {\"role\": \"user\", \"content\": \"Write a short opening paragraph for this story.\"}], \"stream\": false, \"temperature\": 0.7, \"max_tokens\": 1000}"
echo.
echo.

echo 14. 测试列表类型消息 - 问题解决场景（流式）
echo ---------------------------------------------------
echo 测试问题解决相关的对话消息格式
timeout /t 3 /nobreak > nul
curl.exe -X POST "http://localhost:8001/agent/stream" ^
  -H "Content-Type: application/json" ^
  -d "{\"model_name\": \"qwen3:1.7b\", \"mcp_urls\": [\"http://localhost:11435/mcp\"], \"instructions\": \"Help solve problems step by step.\", \"message\": [{\"role\": \"system\", \"content\": \"You are a problem-solving expert.\"}, {\"role\": \"user\", \"content\": \"I have a logic puzzle to solve.\"}, {\"role\": \"assistant\", \"content\": \"I'd be happy to help you solve a logic puzzle! Please describe it.\"}, {\"role\": \"user\", \"content\": \"There are 3 boxes, one contains gold. Each box has a label, but all labels are wrong. How do I find the gold?\"}, {\"role\": \"assistant\", \"content\": \"This is a classic mislabeling puzzle. Let me think through this systematically.\"}, {\"role\": \"user\", \"content\": \"Please explain the solution step by step.\"}], \"stream\": true, \"temperature\": 0.3, \"max_tokens\": 900}"
echo.
echo.

echo 15. 测试列表类型消息 - 技术支持场景（非流式）
echo ---------------------------------------------------
echo 测试技术支持相关的对话消息格式
curl.exe -X POST "http://localhost:8001/agent/run" ^
  -H "Content-Type: application/json" ^
  -d "{\"model_name\": \"qwen3:1.7b\", \"mcp_urls\": [\"http://localhost:11435/mcp\"], \"instructions\": \"Provide technical support and troubleshooting help.\", \"message\": [{\"role\": \"system\", \"content\": \"You are a technical support specialist.\"}, {\"role\": \"user\", \"content\": \"My computer is running very slowly.\"}, {\"role\": \"assistant\", \"content\": \"I can help you troubleshoot the slow performance. Let's start with some basic checks.\"}, {\"role\": \"user\", \"content\": \"It takes 5 minutes to start up and programs freeze frequently.\"}, {\"role\": \"assistant\", \"content\": \"Those symptoms suggest a few possible issues. Let me guide you through some diagnostics.\"}, {\"role\": \"user\", \"content\": \"What should I check first?\"}], \"stream\": false, \"temperature\": 0.1, \"max_tokens\": 800}"
echo.
echo.

echo 16. 测试列表类型消息 - 教学场景（流式）
echo ---------------------------------------------------
echo 测试教学相关的对话消息格式
timeout /t 3 /nobreak > nul
curl.exe -X POST "http://localhost:8001/agent/stream" ^
  -H "Content-Type: application/json" ^
  -d "{\"model_name\": \"qwen3:1.7b\", \"mcp_urls\": [\"http://localhost:11435/mcp\"], \"instructions\": \"You are a patient teacher. Explain concepts clearly.\", \"message\": [{\"role\": \"system\", \"content\": \"You are an experienced mathematics teacher.\"}, {\"role\": \"user\", \"content\": \"I don't understand fractions.\"}, {\"role\": \"assistant\", \"content\": \"Fractions can seem tricky at first, but they're actually quite simple once you understand the concept.\"}, {\"role\": \"user\", \"content\": \"What does 3/4 mean?\"}, {\"role\": \"assistant\", \"content\": \"Great question! 3/4 means you have 3 parts out of 4 equal parts of something.\"}, {\"role\": \"user\", \"content\": \"Can you give me a real-world example?\"}], \"stream\": true, \"temperature\": 0.2, \"max_tokens\": 750}"
echo.
echo.

echo ===================================================
echo 测试完成！
echo ===================================================
pause
