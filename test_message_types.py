#!/usr/bin/env python3
"""
测试脚本：验证 message 字段支持字符串和列表两种类型
"""

import asyncio
import json
import sys
from typing import Union, List, Any
from simpleClient import run_agent_with_mcp

async def test_string_message():
    """测试字符串类型的消息"""
    print("=" * 50)
    print("测试字符串类型的消息")
    print("=" * 50)
    
    try:
        result = await run_agent_with_mcp(
            model_name="qwen3:1.7b",
            mcp_urls=["http://localhost:8000/mcp"],
            instructions="Use the tools to answer the questions.",
            message="Add 5 and 3",
            stream=False,
            temperature=0.0,
            max_tokens=500
        )
        print(f"✅ 字符串消息测试成功: {result}")
        return True
    except Exception as e:
        print(f"❌ 字符串消息测试失败: {str(e)}")
        return False

async def test_list_message():
    """测试列表类型的消息"""
    print("=" * 50)
    print("测试列表类型的消息")
    print("=" * 50)
    
    message_list = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Calculate 7 + 22"},
        {"role": "assistant", "content": "I'll help you calculate that."},
        {"role": "user", "content": "Please show the steps."}
    ]
    
    try:
        result = await run_agent_with_mcp(
            model_name="qwen3:1.7b",
            mcp_urls=["http://localhost:8000/mcp"],
            instructions="Use the tools to answer the questions.",
            message=message_list,
            stream=False,
            temperature=0.0,
            max_tokens=500
        )
        print(f"✅ 列表消息测试成功: {result}")
        return True
    except Exception as e:
        print(f"❌ 列表消息测试失败: {str(e)}")
        return False

async def test_web_service_string():
    """测试 Web 服务的字符串消息"""
    print("=" * 50)
    print("测试 Web 服务 - 字符串消息")
    print("=" * 50)
    
    import aiohttp
    
    request_data = {
        "model_name": "qwen3:1.7b",
        "mcp_urls": ["http://localhost:8000/mcp"],
        "instructions": "Use the tools to answer the questions.",
        "message": "what is 10 + 15?",
        "stream": False,
        "temperature": 0.0,
        "max_tokens": 500
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "http://localhost:8001/agent/run",
                json=request_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Web 服务字符串消息测试成功: {result}")
                    return True
                else:
                    print(f"❌ Web 服务返回错误状态码: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ Web 服务字符串消息测试失败: {str(e)}")
        return False

async def test_web_service_list():
    """测试 Web 服务的列表消息"""
    print("=" * 50)
    print("测试 Web 服务 - 列表消息")
    print("=" * 50)
    
    import aiohttp
    
    request_data = {
        "model_name": "qwen3:1.7b",
        "mcp_urls": ["http://localhost:8000/mcp"],
        "instructions": "Use the tools to answer the questions.",
        "message": [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Calculate 12 * 8"},
            {"role": "assistant", "content": "I'll help you calculate that."},
            {"role": "user", "content": "Please show the calculation."}
        ],
        "stream": False,
        "temperature": 0.0,
        "max_tokens": 500
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "http://localhost:8001/agent/run",
                json=request_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Web 服务列表消息测试成功: {result}")
                    return True
                else:
                    print(f"❌ Web 服务返回错误状态码: {response.status}")
                    error_text = await response.text()
                    print(f"错误详情: {error_text}")
                    return False
    except Exception as e:
        print(f"❌ Web 服务列表消息测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试 message 字段的字符串和列表类型支持")
    print()
    
    tests = [
        ("字符串消息 (simpleClient)", test_string_message),
        ("列表消息 (simpleClient)", test_list_message),
        ("字符串消息 (Web Service)", test_web_service_string),
        ("列表消息 (Web Service)", test_web_service_list),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 执行测试: {test_name}")
        try:
            success = await test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ 测试 {test_name} 发生异常: {str(e)}")
            results.append((test_name, False))
        
        print()
    
    # 打印测试结果摘要
    print("=" * 70)
    print("测试结果摘要")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
        return 0
    else:
        print("⚠️  有一些测试失败，请检查配置或服务状态")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"测试脚本发生错误: {str(e)}")
        sys.exit(1) 