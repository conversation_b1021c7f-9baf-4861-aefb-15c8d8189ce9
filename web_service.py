from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import List, Optional, Any, Union
import asyncio
import json
import logging
from simpleClient import run_agent_with_mcp, create_tool_by_message

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="MCP Agent Web Service",
    description="A web service wrapper for MCP Agent with streaming support",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该更严格
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class AgentRequest(BaseModel):
    """Agent请求模型"""
    model_name: str = "qwen3:1.7b"
    mcp_urls: List[str]
    instructions: str
    message: Union[str, List[Any]]
    stream: bool = True
    temperature: float = 0.0
    max_tokens: int = 1000
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    base_url: str = "http://localhost:11434/v1"
    api_key: str = ""
    use_tracing: bool = False

class AgentResponse(BaseModel):
    """Agent响应模型（非流式）"""
    result: str
    status: str = "success"

class CreateToolRequest(BaseModel):
    """创建工具请求模型"""
    model_name: str = "qwen3:4b"
    message: Union[str, List[Any]]
    temperature: float = 0.0
    max_tokens: int = 1000
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    base_url: str = "http://localhost:11434/v1"
    api_key: str = ""
    use_tracing: bool = False

class CreateToolResponse(BaseModel):
    """创建工具响应模型"""
    message: str
    tool_created: bool = True
    status: str = "success"

@app.get("/")
async def root():
    """健康检查接口"""
    return {"message": "MCP Agent Web Service is running"}

@app.post("/agent/run")
async def run_agent(request: AgentRequest):
    """运行Agent（非流式）"""
    try:
        logger.info(f"Running agent with message: {request.message}")
        
        result = await run_agent_with_mcp(
            model_name=request.model_name,
            mcp_urls=request.mcp_urls,
            instructions=request.instructions,
            message=request.message,
            stream=False,  # 非流式
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            top_p=request.top_p,
            frequency_penalty=request.frequency_penalty,
            presence_penalty=request.presence_penalty,
            base_url=request.base_url,
            api_key=request.api_key,
            use_tracing=request.use_tracing
        )
        
        return AgentResponse(result=str(result))
        
    except Exception as e:
        logger.error(f"Error running agent: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/agent/stream")
async def stream_agent(request: AgentRequest):
    """运行Agent（流式）"""
    try:
        logger.info(f"Running agent with streaming for message: {request.message}")
        
        async def generate_stream():
            """生成流式响应"""
            try:
                # 导入必要的模块
                from agents import Agent, Runner
                from agents.mcp import MCPServerStreamableHttp
                from agents.model_settings import ModelSettings
                from openai.types.responses import ResponseTextDeltaEvent
                from openai import AsyncOpenAI
                from agents import (set_default_openai_client, set_default_openai_api, 
                                  set_tracing_disabled, gen_trace_id, trace)
                
                # 设置OpenAI客户端
                client = AsyncOpenAI(
                    base_url=request.base_url,
                    api_key=request.api_key,
                )
                set_default_openai_client(client=client, use_for_tracing=request.use_tracing)
                set_default_openai_api("chat_completions")
                set_tracing_disabled(disabled=not request.use_tracing)
                
                # 创建MCP服务器列表
                mcp_servers = []
                for i, url in enumerate(request.mcp_urls):
                    server = MCPServerStreamableHttp(
                        name=f"MCP Server {i+1}",
                        params={"url": url},
                    )
                    mcp_servers.append(server)
                
                # 创建ModelSettings
                model_settings = ModelSettings(
                    temperature=request.temperature,
                    max_tokens=request.max_tokens,
                    top_p=request.top_p,
                    frequency_penalty=request.frequency_penalty,
                    presence_penalty=request.presence_penalty,
                )
                
                # 使用 async with 正确管理MCP服务器上下文
                async def run_with_servers():
                    # 创建async with上下文管理器的嵌套结构
                    if len(mcp_servers) == 0:
                        # 没有MCP服务器的情况
                        agent = Agent(
                            name="Assistant",
                            instructions=request.instructions,
                            mcp_servers=[],
                            model=request.model_name,
                            model_settings=model_settings
                        )
                        
                        # 发送开始事件
                        yield f"data: {json.dumps({'type': 'start', 'message': f'Running: {request.message}'})}\n\n"
                        
                        # 流式处理
                        result = Runner.run_streamed(
                            starting_agent=agent, 
                            input=request.message
                        )
                        
                        async for event in result.stream_events():
                            # 处理原始响应事件（token级别的流式输出）
                            if event.type == "raw_response_event" and isinstance(event.data, ResponseTextDeltaEvent):
                                yield f"data: {json.dumps({'type': 'token', 'content': event.data.delta})}\n\n"
                            # 处理运行项目流事件（更高级别的事件）
                            elif event.type == "run_item_stream_event":
                                if event.item.type == "tool_call_item":
                                    yield f"data: {json.dumps({'type': 'tool_call', 'message': 'Tool was called'})}\n\n"
                                elif event.item.type == "tool_call_output_item":
                                    yield f"data: {json.dumps({'type': 'tool_output', 'content': str(event.item.output)})}\n\n"
                            # 处理Agent更新事件
                            elif event.type == "agent_updated_stream_event":
                                yield f"data: {json.dumps({'type': 'agent_updated', 'agent': event.new_agent.name})}\n\n"
                        
                        # 发送完成事件
                        yield f"data: {json.dumps({'type': 'complete', 'message': 'Streaming complete'})}\n\n"
                        
                    elif len(mcp_servers) == 1:
                        # 单个MCP服务器的情况
                        async with mcp_servers[0] as server:
                            agent = Agent(
                                name="Assistant",
                                instructions=request.instructions,
                                mcp_servers=[server],
                                model=request.model_name,
                                model_settings=model_settings
                            )
                            
                            # 发送开始事件
                            yield f"data: {json.dumps({'type': 'start', 'message': f'Running: {request.message}'})}\n\n"
                            
                            # 流式处理
                            result = Runner.run_streamed(
                                starting_agent=agent, 
                                input=request.message
                            )
                            
                            async for event in result.stream_events():
                                # 处理原始响应事件（token级别的流式输出）
                                if event.type == "raw_response_event" and isinstance(event.data, ResponseTextDeltaEvent):
                                    yield f"data: {json.dumps({'type': 'token', 'content': event.data.delta})}\n\n"
                                # 处理运行项目流事件（更高级别的事件）
                                elif event.type == "run_item_stream_event":
                                    if event.item.type == "tool_call_item":
                                        yield f"data: {json.dumps({'type': 'tool_call', 'message': 'Tool was called'})}\n\n"
                                    elif event.item.type == "tool_call_output_item":
                                        yield f"data: {json.dumps({'type': 'tool_output', 'content': str(event.item.output)})}\n\n"
                                # 处理Agent更新事件
                                elif event.type == "agent_updated_stream_event":
                                    yield f"data: {json.dumps({'type': 'agent_updated', 'agent': event.new_agent.name})}\n\n"
                            
                            # 发送完成事件
                            yield f"data: {json.dumps({'type': 'complete', 'message': 'Streaming complete'})}\n\n"
                            
                    else:
                        # 多个MCP服务器的情况 - 使用手动管理但在同一任务中
                        server_contexts = []
                        servers = []
                        
                        try:
                            # 顺序进入所有服务器上下文
                            for server in mcp_servers:
                                entered_server = await server.__aenter__()
                                server_contexts.append(server)
                                servers.append(entered_server)
                            
                            agent = Agent(
                                name="Assistant",
                                instructions=request.instructions,
                                mcp_servers=servers,
                                model=request.model_name,
                                model_settings=model_settings
                            )
                            
                            # 发送开始事件
                            yield f"data: {json.dumps({'type': 'start', 'message': f'Running: {request.message}'})}\n\n"
                            
                            # 流式处理
                            result = Runner.run_streamed(
                                starting_agent=agent, 
                                input=request.message
                            )
                            
                            async for event in result.stream_events():
                                # 处理原始响应事件（token级别的流式输出）
                                if event.type == "raw_response_event" and isinstance(event.data, ResponseTextDeltaEvent):
                                    yield f"data: {json.dumps({'type': 'token', 'content': event.data.delta})}\n\n"
                                # 处理运行项目流事件（更高级别的事件）
                                elif event.type == "run_item_stream_event":
                                    if event.item.type == "tool_call_item":
                                        yield f"data: {json.dumps({'type': 'tool_call', 'message': 'Tool was called'})}\n\n"
                                    elif event.item.type == "tool_call_output_item":
                                        yield f"data: {json.dumps({'type': 'tool_output', 'content': str(event.item.output)})}\n\n"
                                # 处理Agent更新事件
                                elif event.type == "agent_updated_stream_event":
                                    yield f"data: {json.dumps({'type': 'agent_updated', 'agent': event.new_agent.name})}\n\n"
                            
                            # 发送完成事件
                            yield f"data: {json.dumps({'type': 'complete', 'message': 'Streaming complete'})}\n\n"
                            
                        finally:
                            # 逆序退出所有服务器上下文（在同一任务中）
                            for server in reversed(server_contexts):
                                try:
                                    await server.__aexit__(None, None, None)
                                except Exception as cleanup_error:
                                    logger.warning(f"Error during server cleanup: {cleanup_error}")
                
                # 执行流式处理
                async for item in run_with_servers():
                    yield item
                    
            except Exception as e:
                logger.error(f"Error in stream generation: {str(e)}")
                yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"
        
        # 根据是否启用跟踪来包装生成器
        if request.use_tracing:
            from agents import gen_trace_id, trace
            trace_id = gen_trace_id()
            async def traced_generate():
                with trace(workflow_name="MCP Agent Stream", trace_id=trace_id):
                    async for item in generate_stream():
                        yield item
            generator = traced_generate()
        else:
            generator = generate_stream()
        
        return StreamingResponse(
            generator,
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream",
            }
        )
        
    except Exception as e:
        logger.error(f"Error setting up stream: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/tool/create")
async def create_tool(request: CreateToolRequest):
    """创建工具接口"""
    try:
        logger.info(f"Creating tool with message: {request.message}")
        
        await create_tool_by_message(
            model_name=request.model_name,
            message=request.message,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            top_p=request.top_p,
            frequency_penalty=request.frequency_penalty,
            presence_penalty=request.presence_penalty,
            base_url=request.base_url,
            api_key=request.api_key,
            use_tracing=request.use_tracing
        )
        
        return CreateToolResponse(
            message=f"工具创建成功，基于消息: {request.message}",
            tool_created=True,
            status="success"
        )
        
    except Exception as e:
        logger.error(f"Error creating tool: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "service": "MCP Agent Web Service"}

# 示例请求数据
@app.get("/example")
async def get_example():
    """获取示例请求数据"""
    return {
        "example_request_string": {
            "model_name": "qwen3:1.7b",
            "mcp_urls": ["http://localhost:8000/mcp"],
            "instructions": "Use the tools to answer the questions.",
            "message": "what's (36 + 51) x 123899?",
            "stream": True,
            "temperature": 0.0,
            "max_tokens": 1000,
            "top_p": 1.0,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "base_url": "http://localhost:11434/v1",
            "api_key": "",
            "use_tracing": False
        },
        "example_request_list": {
            "model_name": "qwen3:1.7b",
            "mcp_urls": ["http://localhost:8000/mcp"],
            "instructions": "Use the tools to answer the questions.",
            "message": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Calculate (36 + 51) x 123899"},
                {"role": "assistant", "content": "I'll help you calculate that."},
                {"role": "user", "content": "Please show the steps."}
            ],
            "stream": True,
            "temperature": 0.0,
            "max_tokens": 1000,
            "top_p": 1.0,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "base_url": "http://localhost:11434/v1",
            "api_key": "",
            "use_tracing": False
        },
        "example_create_tool": {
            "model_name": "qwen3:4b",
            "message": "create a tool to add text to image",
            "temperature": 0.0,
            "max_tokens": 1000,
            "top_p": 1.0,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "base_url": "http://localhost:11434/v1",
            "api_key": "",
            "use_tracing": False
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "web_service:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    ) 