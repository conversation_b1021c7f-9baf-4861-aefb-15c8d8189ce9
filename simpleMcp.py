# weather_server.py
from typing import List
from fastmcp import FastMCP
from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware

# 配置 CORS middleware
cors_middleware = [
    Middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 在生产环境中应该指定具体的域名
        allow_credentials=True,
        allow_methods=["GET", "POST", "OPTIONS"],
        allow_headers=["*"],
    ),
]

mcp = FastMCP("Weather")

@mcp.tool()
async def get_weather(location: str) -> str:
    """Get weather for location."""
    return f"It's always sunny in {location}"

@mcp.tool()
def add(a: int, b: int) -> int:
    """Add two numbers"""
    print(f"Adding {a} and {b}")
    return a + b

@mcp.tool()
def multiply(a: int, b: int) -> int:
    """Multiply two numbers"""
    print(f"Multiplying {a} and {b}")
    return a * b
if __name__ == "__main__":
    #设置端口为11435
    # 创建带有 CORS middleware 的 HTTP 应用，启用 stateless 模式
    http_app = mcp.http_app(
        middleware=cors_middleware,
        transport='streamable-http',
        stateless_http=True  # 启用 stateless 模式
    )
    
    # 使用 uvicorn 运行应用
    import uvicorn
    uvicorn.run(http_app, host="127.0.0.1", port=11435)