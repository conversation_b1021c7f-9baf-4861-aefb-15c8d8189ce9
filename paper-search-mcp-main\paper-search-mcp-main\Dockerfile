# Generated by https://smithery.ai. See: https://smithery.ai/docs/config#dockerfile
FROM python:3.10-alpine

# Install system dependencies
RUN apk add --no-cache build-base libffi-dev openssl-dev

WORKDIR /app

# Copy the entire repository
COPY . .

# Upgrade pip and install dependencies
RUN pip install --upgrade pip \
    && pip install --no-cache-dir .

# Expose port if necessary (MCP servers use stdio; no port to expose)

# Command to run the MCP server
CMD ["python", "-m", "paper_search_mcp.server"]
