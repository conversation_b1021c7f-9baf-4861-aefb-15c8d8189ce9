#!/usr/bin/env python3
"""
工具创建API使用示例
"""

import requests
import json
import time

def create_tool_example():
    """创建工具的示例"""
    url = "http://localhost:8001/tool/create"
    
    # 示例1: 创建数学计算工具
    print("=== 示例1: 创建圆形面积计算工具 ===")
    data1 = {
        "model_name": "qwen3:4b",
        "message": "create a tool to calculate the area of a circle given its radius",
        "temperature": 0.0,
        "max_tokens": 1000
    }
    
    try:
        response = requests.post(url, json=data1)
        if response.status_code == 200:
            result = response.json()
            print(f"✓ 成功: {result['message']}")
        else:
            print(f"✗ 失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"✗ 错误: {e}")
    
    time.sleep(2)
    
    # 示例2: 创建字符串处理工具
    print("\n=== 示例2: 创建字符串反转工具 ===")
    data2 = {
        "model_name": "qwen3:4b",
        "message": "create a tool to reverse a string and return the reversed string",
        "temperature": 0.0,
        "max_tokens": 1000
    }
    
    try:
        response = requests.post(url, json=data2)
        if response.status_code == 200:
            result = response.json()
            print(f"✓ 成功: {result['message']}")
        else:
            print(f"✗ 失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"✗ 错误: {e}")
    
    time.sleep(2)
    
    # 示例3: 创建文件操作工具
    print("\n=== 示例3: 创建文件读取工具 ===")
    data3 = {
        "model_name": "qwen3:4b",
        "message": "create a tool to read the contents of a text file and return the content as a string",
        "temperature": 0.0,
        "max_tokens": 1000
    }
    
    try:
        response = requests.post(url, json=data3)
        if response.status_code == 200:
            result = response.json()
            print(f"✓ 成功: {result['message']}")
        else:
            print(f"✗ 失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"✗ 错误: {e}")

def check_service_health():
    """检查服务健康状态"""
    print("=== 检查服务状态 ===")
    try:
        response = requests.get("http://localhost:8001/health")
        if response.status_code == 200:
            result = response.json()
            print(f"✓ 服务正常: {result}")
            return True
        else:
            print(f"✗ 服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 无法连接到服务: {e}")
        print("请确保Web服务正在运行: python web_service.py")
        return False

def get_examples():
    """获取API示例"""
    print("\n=== 获取API示例 ===")
    try:
        response = requests.get("http://localhost:8001/example")
        if response.status_code == 200:
            examples = response.json()
            print("✓ 获取示例成功")
            print(f"可用示例: {list(examples.keys())}")
            
            # 显示工具创建示例
            if "example_create_tool" in examples:
                print("\n工具创建示例:")
                print(json.dumps(examples["example_create_tool"], indent=2, ensure_ascii=False))
        else:
            print(f"✗ 获取示例失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 获取示例时出错: {e}")

if __name__ == "__main__":
    print("MCP工具创建API使用示例")
    print("=" * 50)
    
    # 检查服务状态
    if not check_service_health():
        exit(1)
    
    # 获取示例
    get_examples()
    
    print("\n" + "=" * 50)
    
    # 创建工具示例
    create_tool_example()
    
    print("\n" + "=" * 50)
    print("示例完成！")
    print("\n提示:")
    print("1. 创建的工具会保存在 tools/ 目录中")
    print("2. 可以查看生成的Python文件来了解工具的具体实现")
    print("3. 使用 python test_tool_creation_api.py 进行更全面的测试") 