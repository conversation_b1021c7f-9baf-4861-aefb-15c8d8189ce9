import asyncio
from dataclasses import dataclass
from agents import Agent, RunContextWrapper, <PERSON>, function_tool
from openai import Async<PERSON>penA<PERSON>
from agents import (set_default_openai_client, set_default_openai_api,
                                  set_tracing_disabled)

# 设置OpenAI客户端
client = AsyncOpenAI(
    base_url="http://localhost:11434/v1",
    api_key="ollama",
)
set_default_openai_client(client=client)
set_default_openai_api("chat_completions")
set_tracing_disabled(disabled=True)

async def main():

    agent = Agent(
        name="Assistant",
        model="qwen2.5-coder:0.5b"
    )

    result = await Runner.run(
        starting_agent=agent,
        input="What is the age of the user?",
        max_turns=5  # 控制轮数
    )

    print(result.final_output)
if __name__ == "__main__":
    asyncio.run(main())