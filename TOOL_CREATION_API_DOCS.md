# 工具创建Web服务API文档

## 概述

该Web服务提供了一个HTTP API接口，用于通过自然语言描述自动创建Python工具函数。服务基于FastAPI构建，支持异步处理。

## 基础信息

- **服务地址**: `http://localhost:8001`
- **API版本**: 1.0.0
- **支持的HTTP方法**: POST
- **内容类型**: application/json

## API端点

### 1. 创建工具 - `/tool/create`

**方法**: `POST`
**路径**: `/tool/create`
**描述**: 根据自然语言描述创建Python工具函数

#### 请求参数

| 参数名                | 类型         | 必需 | 默认值                      | 描述                   |
| --------------------- | ------------ | ---- | --------------------------- | ---------------------- |
| `model_name`        | string       | 否   | "qwen3:4b"                  | 使用的LLM模型名称      |
| `message`           | string/array | 是   | -                           | 工具创建的自然语言描述 |
| `temperature`       | float        | 否   | 0.0                         | 模型温度参数 (0.0-2.0) |
| `max_tokens`        | integer      | 否   | 1000                        | 最大生成token数        |
| `top_p`             | float        | 否   | 1.0                         | Top-p采样参数          |
| `frequency_penalty` | float        | 否   | 0.0                         | 频率惩罚参数           |
| `presence_penalty`  | float        | 否   | 0.0                         | 存在惩罚参数           |
| `base_url`          | string       | 否   | "http://localhost:11434/v1" | LLM API基础URL         |
| `api_key`           | string       | 否   | ""                          | API密钥                |
| `use_tracing`       | boolean      | 否   | false                       | 是否启用追踪           |

#### 请求示例

```json
{
    "model_name": "qwen3:4b",
    "message": "create a tool to add text to image",
    "temperature": 0.0,
    "max_tokens": 1000,
    "top_p": 1.0,
    "frequency_penalty": 0.0,
    "presence_penalty": 0.0,
    "base_url": "http://localhost:11434/v1",
    "api_key": "",
    "use_tracing": false
}
```

#### 响应格式

**成功响应 (200)**:

```json
{
    "message": "工具创建成功，基于消息: create a tool to add text to image",
    "tool_created": true,
    "status": "success"
}
```

**错误响应 (500)**:

```json
{
    "detail": "错误详细信息"
}
```

## 使用示例

### Python请求示例

```python
import requests
import json

# 创建工具请求
url = "http://localhost:8001/tool/create"
data = {
    "model_name": "qwen3:4b",
    "message": "create a tool to calculate the area of a circle",
    "temperature": 0.0,
    "max_tokens": 1000
}

response = requests.post(url, json=data)
if response.status_code == 200:
    result = response.json()
    print(f"工具创建状态: {result['status']}")
    print(f"响应消息: {result['message']}")
else:
    print(f"错误: {response.status_code} - {response.text}")
```

### curl命令示例

```bash
curl -X POST "http://localhost:8001/tool/create" \
     -H "Content-Type: application/json" \
     -d '{
         "model_name": "qwen3:4b",
         "message": "create a tool to convert temperature from Celsius to Fahrenheit",
         "temperature": 0.0,
         "max_tokens": 1000
     }'
```

### JavaScript/Node.js示例

```javascript
const axios = require('axios');

async function createTool() {
    try {
        const response = await axios.post('http://localhost:8001/tool/create', {
            model_name: "qwen3:4b",
            message: "create a tool to generate random passwords",
            temperature: 0.0,
            max_tokens: 1000
        });
      
        console.log('工具创建成功:', response.data);
    } catch (error) {
        console.error('错误:', error.response?.data || error.message);
    }
}

createTool();
```

## 其他API端点

### 获取示例 - `/example`

**方法**: `GET`
**路径**: `/example`
**描述**: 获取各种API调用的示例数据

```bash
curl -X GET "http://localhost:8001/example"
```

### 健康检查 - `/health`

**方法**: `GET`
**路径**: `/health`
**描述**: 检查服务健康状态

```bash
curl -X GET "http://localhost:8001/health"
```

## 工具创建流程

1. **接收请求**: 服务接收包含工具描述的POST请求
2. **LLM处理**: 使用指定的LLM模型分析描述并生成结构化的工具定义
3. **工具生成**: 系统根据LLM输出创建Python函数文件
4. **文件保存**: 生成的工具保存到 `tools/`目录
5. **响应返回**: 返回创建状态和相关信息

## 生成的工具结构

生成的工具包含以下部分：

- **name**: 工具名称
- **description**: 工具描述
- **code**: Python函数代码
- **import_code**: 必要的导入语句

## 注意事项

1. **模型要求**: 确保指定的LLM模型已在本地运行
2. **依赖管理**: 生成的工具可能需要额外的Python包
3. **文件权限**: 确保服务有权限在 `tools/`目录创建文件
4. **错误处理**: 如果工具创建失败，检查LLM服务状态和网络连接

## 启动服务

```bash
# 启动Web服务
python web_service.py

# 或使用uvicorn
uvicorn web_service:app --host 0.0.0.0 --port 8001 --reload
```

## 故障排除

### 常见错误

1. **连接错误**: 检查LLM服务是否运行在指定的base_url
2. **模型不存在**: 确认model_name在LLM服务中可用
3. **权限错误**: 检查文件系统权限
4. **导入错误**: 确保所有依赖包已安装

### 调试建议

1. 启用 `use_tracing`参数获取详细日志
2. 检查服务日志获取错误信息
3. 使用 `/health`端点验证服务状态
4. 通过 `/example`端点获取正确的请求格式
