#!/usr/bin/env python3
import uvicorn
import sys
import os

def main():
    """启动 MCP Agent Web Service"""
    print("🚀 启动 MCP Agent Web Service...")
    print("=" * 50)
    
    # 设置默认参数
    host = "0.0.0.0"
    port = 8001
    reload = True
    log_level = "info"
    
    # 从命令行参数获取配置
    if len(sys.argv) > 1:
        for arg in sys.argv[1:]:
            if arg.startswith("--host="):
                host = arg.split("=")[1]
            elif arg.startswith("--port="):
                port = int(arg.split("=")[1])
            elif arg == "--no-reload":
                reload = False
            elif arg.startswith("--log-level="):
                log_level = arg.split("=")[1]
    
    print(f"🌐 服务地址: http://{host}:{port}")
    print(f"🔄 自动重载: {reload}")
    print(f"📝 日志级别: {log_level}")
    print("=" * 50)
    print("📚 可用的API端点:")
    print(f"  - GET  http://{host}:{port}/           - 根路径")
    print(f"  - GET  http://{host}:{port}/health     - 健康检查")
    print(f"  - GET  http://{host}:{port}/example    - 示例请求")
    print(f"  - POST http://{host}:{port}/agent/run    - 运行Agent(非流式)")
    print(f"  - POST http://{host}:{port}/agent/stream - 运行Agent(流式)")
    print("=" * 50)
    print("💡 测试命令:")
    print("  python test_client.py stream     # 测试流式端点")
    print("  python test_client.py nostream   # 测试非流式端点")
    print("=" * 50)
    
    try:
        uvicorn.run(
            "web_service:app",
            host=host,
            port=port,
            reload=reload,
            log_level=log_level
        )
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动服务时出错: {str(e)}")

if __name__ == "__main__":
    main() 