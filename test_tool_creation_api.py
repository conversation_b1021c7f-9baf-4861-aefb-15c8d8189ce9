#!/usr/bin/env python3
"""
工具创建API测试脚本
"""

import requests
import json
import time
import sys

def test_tool_creation_api():
    """测试工具创建API"""
    base_url = "http://localhost:8001"
    
    # 测试健康检查
    print("=== 测试健康检查 ===")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✓ 健康检查通过")
            print(f"响应: {response.json()}")
        else:
            print(f"✗ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 无法连接到服务: {e}")
        print("请确保Web服务正在运行: python web_service.py")
        return False
    
    print("\n" + "="*50)
    
    # 测试获取示例
    print("=== 测试获取示例 ===")
    try:
        response = requests.get(f"{base_url}/example")
        if response.status_code == 200:
            examples = response.json()
            print("✓ 获取示例成功")
            if "example_create_tool" in examples:
                print("✓ 工具创建示例存在")
            else:
                print("✗ 工具创建示例不存在")
        else:
            print(f"✗ 获取示例失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 获取示例时出错: {e}")
    
    print("\n" + "="*50)
    
    # 测试工具创建
    print("=== 测试工具创建 ===")
    
    test_cases = [
        {
            "name": "简单数学工具",
            "message": "create a tool to calculate the area of a circle",
            "expected_keywords": ["circle", "area", "math", "pi"]
        },
        {
            "name": "字符串处理工具",
            "message": "create a tool to reverse a string",
            "expected_keywords": ["reverse", "string"]
        },
        {
            "name": "文件操作工具",
            "message": "create a tool to read text from a file",
            "expected_keywords": ["read", "file", "text"]
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i}: {test_case['name']} ---")
        
        # 准备请求数据
        data = {
            "model_name": "qwen3:4b",
            "message": test_case["message"],
            "temperature": 0.0,
            "max_tokens": 1000,
            "top_p": 1.0,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "base_url": "http://localhost:11434/v1",
            "api_key": "",
            "use_tracing": False
        }
        
        try:
            print(f"发送请求: {test_case['message']}")
            response = requests.post(f"{base_url}/tool/create", json=data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                print("✓ 工具创建成功")
                print(f"状态: {result.get('status')}")
                print(f"消息: {result.get('message')}")
                print(f"工具已创建: {result.get('tool_created')}")
            else:
                print(f"✗ 工具创建失败: {response.status_code}")
                print(f"错误详情: {response.text}")
                
        except requests.exceptions.Timeout:
            print("✗ 请求超时 - 可能是LLM服务响应慢")
        except Exception as e:
            print(f"✗ 请求失败: {e}")
        
        # 在测试用例之间稍作停顿
        if i < len(test_cases):
            time.sleep(2)
    
    print("\n" + "="*50)
    print("=== 测试完成 ===")

def test_with_curl_examples():
    """打印curl测试示例"""
    print("\n=== curl测试示例 ===")
    
    curl_examples = [
        {
            "name": "基本工具创建",
            "command": '''curl -X POST "http://localhost:8001/tool/create" \\
     -H "Content-Type: application/json" \\
     -d '{
         "model_name": "qwen3:4b",
         "message": "create a tool to calculate factorial of a number",
         "temperature": 0.0,
         "max_tokens": 1000
     }' '''
        },
        {
            "name": "健康检查",
            "command": 'curl -X GET "http://localhost:8001/health"'
        },
        {
            "name": "获取示例",
            "command": 'curl -X GET "http://localhost:8001/example"'
        }
    ]
    
    for example in curl_examples:
        print(f"\n--- {example['name']} ---")
        print(example['command'])

if __name__ == "__main__":
    print("工具创建API测试脚本")
    print("="*50)
    
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--curl":
        test_with_curl_examples()
    else:
        test_tool_creation_api()
        
    print("\n注意事项:")
    print("1. 确保Web服务正在运行: python web_service.py")
    print("2. 确保LLM服务正在运行 (默认: http://localhost:11434)")
    print("3. 使用 --curl 参数查看curl测试示例") 