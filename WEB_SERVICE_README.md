# MCP Agent Web Service

这是一个基于 FastAPI 的 Web 服务，包装了 `simpleClient.py` 的功能，提供了 RESTful API 接口和流式访问支持。

## 🚀 功能特性

- **流式响应**: 支持实时流式输出，可以看到 Agent 逐步生成响应
- **非流式响应**: 传统的请求-响应模式
- **RESTful API**: 标准的 HTTP 接口
- **CORS 支持**: 跨域资源共享
- **健康检查**: 服务状态监控
- **详细日志**: 请求和错误日志记录
- **异步处理**: 高性能异步处理

## 📦 安装依赖

```bash
pip install -r requirements.txt
```

## 🔧 启动服务

### 方式一：使用启动脚本（推荐）

```bash
python start_service.py
```

### 方式二：直接使用 uvicorn

```bash
uvicorn web_service:app --host 0.0.0.0 --port 8001 --reload
```

### 启动参数

```bash
python start_service.py --host=localhost --port=8002 --no-reload --log-level=debug
```

## 🌐 API 端点

### 1. 健康检查

```http
GET /health
```

**响应示例：**

```json
{
  "status": "healthy",
  "service": "MCP Agent Web Service"
}
```

### 2. 示例请求

```http
GET /example
```

**响应示例：**

```json
{
  "example_request_string": {
    "model_name": "qwen3:1.7b",
    "mcp_urls": ["http://localhost:8000/mcp"],
    "instructions": "Use the tools to answer the questions.",
    "message": "what's (36 + 51) x 123899?",
    "stream": true,
    "temperature": 0.0,
    "max_tokens": 1000,
    "top_p": 1.0,
    "frequency_penalty": 0.0,
    "presence_penalty": 0.0,
    "base_url": "http://localhost:11434/v1",
    "api_key": "",
    "use_tracing": false
  },
  "example_request_list": {
    "model_name": "qwen3:1.7b",
    "mcp_urls": ["http://localhost:8000/mcp"],
    "instructions": "Use the tools to answer the questions.",
    "message": [
      {"role": "system", "content": "You are a helpful assistant."},
      {"role": "user", "content": "Calculate (36 + 51) x 123899"},
      {"role": "assistant", "content": "I'll help you calculate that."},
      {"role": "user", "content": "Please show the steps."}
    ],
    "stream": true,
    "temperature": 0.0,
    "max_tokens": 1000,
    "top_p": 1.0,
    "frequency_penalty": 0.0,
    "presence_penalty": 0.0,
    "base_url": "http://localhost:11434/v1",
    "api_key": "",
    "use_tracing": false
  }
}
```

### 3. 运行 Agent（非流式）

```http
POST /agent/run
```

**请求体：**

```json
{
  "model_name": "qwen3:1.7b",
  "mcp_urls": ["http://localhost:8000/mcp"],
  "instructions": "Use the tools to answer the questions.",
  "message": "Add these numbers: 7 and 22.",
  "stream": false,
  "temperature": 0.1,
  "max_tokens": 500,
  "top_p": 1.0,
  "frequency_penalty": 0.0,
  "presence_penalty": 0.0,
  "base_url": "http://localhost:11434/v1",
  "api_key": "",
  "use_tracing": false
}
```

**响应示例：**

```json
{
  "result": "The sum of 7 and 22 is 29.",
  "status": "success"
}
```

### 4. 运行 Agent（流式）

```http
POST /agent/stream
```

**请求体：** 同上，但 `stream` 字段会被忽略

**响应格式：** Server-Sent Events (SSE)

**响应示例：**

```
data: {"type": "start", "message": "Running: what's (36 + 51) x 123899?"}

data: {"type": "token", "content": "Let"}

data: {"type": "token", "content": " me"}

data: {"type": "tool_call", "message": "Tool was called"}

data: {"type": "tool_output", "content": "10789613"}

data: {"type": "complete", "message": "Streaming complete"}
```

## 📱 测试客户端

### 使用提供的测试脚本

```bash
# 测试流式端点
python test_client.py stream

# 测试非流式端点
python test_client.py nostream

# 默认测试流式端点
python test_client.py
```

### 使用 curl 测试

```bash
# 健康检查
curl -X GET http://localhost:8001/health

# 非流式请求
curl -X POST http://localhost:8001/agent/run \
  -H "Content-Type: application/json" \
  -d '{
    "model_name": "qwen3:1.7b",
    "mcp_urls": ["http://localhost:8000/mcp"],
    "instructions": "Use the tools to answer the questions.",
    "message": "Add 5 and 3",
    "stream": false
  }'

# 流式请求（字符串消息）
curl -X POST http://localhost:8001/agent/stream \
  -H "Content-Type: application/json" \
  -d '{
    "model_name": "qwen3:1.7b",
    "mcp_urls": ["http://localhost:8000/mcp"],
    "instructions": "Use the tools to answer the questions.",
    "message": "what is 2 + 2?",
    "stream": true
  }'

# 流式请求（列表消息）
curl -X POST http://localhost:8001/agent/stream \
  -H "Content-Type: application/json" \
  -d '{
    "model_name": "qwen3:1.7b",
    "mcp_urls": ["http://localhost:8000/mcp"],
    "instructions": "Use the tools for calculations. Show detailed steps.",
    "message": [
      {"role": "system", "content": "You are a scientific calculator assistant."},
      {"role": "user", "content": "I need to calculate the area of a circle."},
      {"role": "assistant", "content": "I can help you calculate the area. The formula is π × r²"},
      {"role": "user", "content": "The radius is 5 units. Please calculate it step by step."}
    ],
    "stream": true
  }'

# 非流式请求（列表消息）
curl -X POST http://localhost:8001/agent/run \
  -H "Content-Type: application/json" \
  -d '{
    "model_name": "qwen3:1.7b",
    "mcp_urls": ["http://localhost:8000/mcp"],
    "instructions": "You are a coding assistant. Help users with programming questions.",
    "message": [
      {"role": "system", "content": "You are an expert Python programmer."},
      {"role": "user", "content": "How do I create a list in Python?"},
      {"role": "assistant", "content": "You can create a list using square brackets: my_list = [1, 2, 3]"},
      {"role": "user", "content": "What about adding elements to it?"},
      {"role": "assistant", "content": "You can use append() method: my_list.append(4)"},
      {"role": "user", "content": "Can you show me a complete example?"}
    ],
    "stream": false
  }'
```

### 使用 JavaScript 测试

#### 流式请求示例（字符串消息）

```javascript
const response = await fetch('http://localhost:8001/agent/stream', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    model_name: 'qwen3:1.7b',
    mcp_urls: ['http://localhost:8000/mcp'],
    instructions: 'Use the tools to answer the questions.',
    message: 'what is 10 + 5?',
    stream: true
  })
});

const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  
  const chunk = decoder.decode(value);
  const lines = chunk.split('\n');
  
  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const data = JSON.parse(line.substring(6));
    
      if (data.type === 'token') {
        process.stdout.write(data.content);
      } else if (data.type === 'tool_call') {
        console.log(`\n🔧 ${data.message}`);
      } else if (data.type === 'tool_output') {
        console.log(`\n📋 ${data.content}`);
      }
    }
  }
}
```

#### 流式请求示例（列表消息）

```javascript
const response = await fetch('http://localhost:8001/agent/stream', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    model_name: 'qwen3:1.7b',
    mcp_urls: ['http://localhost:8000/mcp'],
    instructions: 'You are a patient teacher. Explain concepts clearly.',
    message: [
      {"role": "system", "content": "You are an experienced mathematics teacher."},
      {"role": "user", "content": "I don't understand fractions."},
      {"role": "assistant", "content": "Fractions can seem tricky at first, but they're actually quite simple once you understand the concept."},
      {"role": "user", "content": "What does 3/4 mean?"},
      {"role": "assistant", "content": "Great question! 3/4 means you have 3 parts out of 4 equal parts of something."},
      {"role": "user", "content": "Can you give me a real-world example?"}
    ],
    stream: true,
    temperature: 0.2,
    max_tokens: 750
  })
});

// 处理流式响应的代码相同
const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  
  const chunk = decoder.decode(value);
  const lines = chunk.split('\n');
  
  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const data = JSON.parse(line.substring(6));
    
      if (data.type === 'token') {
        process.stdout.write(data.content);
      } else if (data.type === 'tool_call') {
        console.log(`\n🔧 ${data.message}`);
      } else if (data.type === 'tool_output') {
        console.log(`\n📋 ${data.content}`);
      } else if (data.type === 'complete') {
        console.log(`\n✅ ${data.message}`);
      }
    }
  }
}
```

#### 非流式请求示例（列表消息）

```javascript
const response = await fetch('http://localhost:8001/agent/run', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    model_name: 'qwen3:1.7b',
    mcp_urls: ['http://localhost:8000/mcp'],
    instructions: 'Provide technical support and troubleshooting help.',
    message: [
      {"role": "system", "content": "You are a technical support specialist."},
      {"role": "user", "content": "My computer is running very slowly."},
      {"role": "assistant", "content": "I can help you troubleshoot the slow performance. Let's start with some basic checks."},
      {"role": "user", "content": "It takes 5 minutes to start up and programs freeze frequently."},
      {"role": "assistant", "content": "Those symptoms suggest a few possible issues. Let me guide you through some diagnostics."},
      {"role": "user", "content": "What should I check first?"}
    ],
    stream: false,
    temperature: 0.1,
    max_tokens: 800
  })
});

const result = await response.json();
console.log('Result:', result.result);
console.log('Status:', result.status);
```

## 🔧 请求参数说明

| 参数                  | 类型    | 默认值                          | 说明                                                  |
| --------------------- | ------- | ------------------------------- | ----------------------------------------------------- |
| `model_name`        | string  | `"qwen3:1.7b"`                | 模型名称                                              |
| `mcp_urls`          | array   | -                               | MCP 服务器 URL 列表                                   |
| `instructions`      | string  | -                               | Agent 的系统指令                                      |
| `message`           | string/array | -                             | 要处理的消息（支持字符串或消息列表格式）               |
| `stream`            | boolean | `true`                          | 是否启用流式输出（在 `/agent/stream` 中此参数被忽略） |
| `temperature`       | number  | `0.0`                           | 温度参数 (0.0-2.0)                                    |
| `max_tokens`        | number  | `1000`                          | 最大 token 数                                         |
| `top_p`             | number  | `1.0`                           | Top-p 参数                                            |
| `frequency_penalty` | number  | `0.0`                           | 频率惩罚                                              |
| `presence_penalty`  | number  | `0.0`                           | 存在惩罚                                              |
| `base_url`          | string  | `"http://localhost:11434/v1"`   | OpenAI API 基础 URL                                   |
| `api_key`           | string  | `""`                            | API 密钥                                              |
| `use_tracing`       | boolean | `false`                         | 是否启用跟踪                                          |

### 消息格式说明

`message` 字段现在支持两种格式：

#### 1. 字符串格式（简单消息）
```json
{
  "message": "what's (36 + 51) x 123899?"
}
```

#### 2. 列表格式（包含上下文的对话）
```json
{
  "message": [
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "Calculate (36 + 51) x 123899"},
    {"role": "assistant", "content": "I'll help you calculate that."},
    {"role": "user", "content": "Please show the steps."}
  ]
}
```

#### 列表格式支持的角色类型：
- `system`: 系统消息，用于设置 AI 的行为和个性
- `user`: 用户消息
- `assistant`: AI 助手的回复

#### 列表格式使用场景和示例

**场景1：编程助手**
```json
{
  "message": [
    {"role": "system", "content": "You are an expert Python programmer."},
    {"role": "user", "content": "How do I create a list in Python?"},
    {"role": "assistant", "content": "You can create a list using square brackets: my_list = [1, 2, 3]"},
    {"role": "user", "content": "What about adding elements to it?"},
    {"role": "assistant", "content": "You can use append() method: my_list.append(4)"},
    {"role": "user", "content": "Can you show me a complete example?"}
  ]
}
```

**场景2：数学教学**
```json
{
  "message": [
    {"role": "system", "content": "You are an experienced mathematics teacher."},
    {"role": "user", "content": "I don't understand fractions."},
    {"role": "assistant", "content": "Fractions can seem tricky at first, but they're actually quite simple once you understand the concept."},
    {"role": "user", "content": "What does 3/4 mean?"},
    {"role": "assistant", "content": "Great question! 3/4 means you have 3 parts out of 4 equal parts of something."},
    {"role": "user", "content": "Can you give me a real-world example?"}
  ]
}
```

**场景3：科学计算**
```json
{
  "message": [
    {"role": "system", "content": "You are a scientific calculator assistant."},
    {"role": "user", "content": "I need to calculate the area of a circle."},
    {"role": "assistant", "content": "I can help you calculate the area. The formula is π × r²"},
    {"role": "user", "content": "The radius is 5 units. Please calculate it step by step."}
  ]
}
```

**场景4：技术支持**
```json
{
  "message": [
    {"role": "system", "content": "You are a technical support specialist."},
    {"role": "user", "content": "My computer is running very slowly."},
    {"role": "assistant", "content": "I can help you troubleshoot the slow performance. Let's start with some basic checks."},
    {"role": "user", "content": "It takes 5 minutes to start up and programs freeze frequently."},
    {"role": "assistant", "content": "Those symptoms suggest a few possible issues. Let me guide you through some diagnostics."},
    {"role": "user", "content": "What should I check first?"}
  ]
}
```

**场景5：创意写作**
```json
{
  "message": [
    {"role": "system", "content": "You are a professional creative writer."},
    {"role": "user", "content": "I want to write a story about time travel."},
    {"role": "assistant", "content": "That's an interesting topic! Time travel stories offer many possibilities."},
    {"role": "user", "content": "What if someone travels back 100 years?"},
    {"role": "assistant", "content": "Going back 100 years would mean arriving in the 1920s - a fascinating era!"},
    {"role": "user", "content": "Write a short opening paragraph for this story."}
  ]
}
```

#### 列表格式最佳实践

1. **系统消息置顶**: 总是将 `system` 角色的消息放在列表开头
2. **对话连贯性**: 确保对话流程自然，前后文相关
3. **角色一致性**: 保持每个角色的设定一致
4. **上下文长度**: 控制对话历史长度，避免过长影响性能
5. **明确意图**: 最后一个用户消息应该明确表达当前需求

## 🔍 流式响应事件类型

| 事件类型          | 说明         |
| ----------------- | ------------ |
| `start`         | 开始处理     |
| `token`         | 生成的 token |
| `tool_call`     | 工具调用     |
| `tool_output`   | 工具输出     |
| `agent_updated` | Agent 更新   |
| `complete`      | 处理完成     |
| `error`         | 错误信息     |

## 📊 性能优化

- 使用异步处理提高并发性能
- 支持连接池复用
- 流式响应减少延迟
- 自动资源清理

## 🐛 故障排除

### 常见问题

1. **端口被占用**

   ```bash
   # 查看端口占用
   netstat -ano | findstr :8001
   # 或使用不同端口
   python start_service.py --port=8002
   ```
2. **MCP 服务器连接失败**

   - 确保 MCP 服务器正在运行
   - 检查 URL 是否正确
   - 确认网络连接
3. **模型不可用**

   - 确保 Ollama 服务正在运行
   - 检查模型是否已下载
   - 验证 base_url 是否正确

### 日志查看

服务启动后会在控制台输出详细日志，包括：

- 请求日志
- 错误信息
- 性能统计

## 🔒 安全考虑

- 在生产环境中，请修改 CORS 设置
- 使用适当的 API 密钥
- 考虑添加身份验证
- 限制请求频率

## 🎯 高级用法：列表消息场景

### 多轮对话管理

#### 1. 保持对话状态
```bash
# 第一次请求：建立上下文
curl -X POST http://localhost:8001/agent/run \
  -H "Content-Type: application/json" \
  -d '{
    "message": [
      {"role": "system", "content": "You are a helpful coding assistant."},
      {"role": "user", "content": "I need help with Python lists."}
    ]
  }'

# 后续请求：添加对话历史
curl -X POST http://localhost:8001/agent/run \
  -H "Content-Type: application/json" \
  -d '{
    "message": [
      {"role": "system", "content": "You are a helpful coding assistant."},
      {"role": "user", "content": "I need help with Python lists."},
      {"role": "assistant", "content": "I would be happy to help you with Python lists..."},
      {"role": "user", "content": "How do I remove duplicates from a list?"}
    ]
  }'
```

#### 2. 角色切换场景
```json
{
  "message": [
    {"role": "system", "content": "You are a professional interviewer."},
    {"role": "user", "content": "I'm preparing for a technical interview."},
    {"role": "assistant", "content": "Great! I'll help you prepare. What position are you interviewing for?"},
    {"role": "user", "content": "Software Engineer at a tech company."},
    {"role": "assistant", "content": "Perfect! Let's start with some common questions."},
    {"role": "user", "content": "Ask me about data structures."}
  ]
}
```

#### 3. 错误处理和重试
```json
{
  "message": [
    {"role": "system", "content": "You are a debugging assistant."},
    {"role": "user", "content": "My code has an error."},
    {"role": "assistant", "content": "I can help you debug. Please share the error message."},
    {"role": "user", "content": "IndexError: list index out of range"},
    {"role": "assistant", "content": "This error occurs when trying to access a list index that doesn't exist."},
    {"role": "user", "content": "Here's my code: for i in range(10): print(my_list[i])"}
  ]
}
```

### 复杂对话模式

#### 1. 层次化指导
```json
{
  "message": [
    {"role": "system", "content": "You are a patient programming tutor."},
    {"role": "user", "content": "I'm completely new to programming."},
    {"role": "assistant", "content": "Welcome to programming! Let's start with the basics."},
    {"role": "user", "content": "What's a variable?"},
    {"role": "assistant", "content": "A variable is like a box that stores data..."},
    {"role": "user", "content": "Can you show me an example?"},
    {"role": "assistant", "content": "Sure! Here's a simple example: name = 'Alice'"},
    {"role": "user", "content": "Now how do I use this variable?"}
  ]
}
```

#### 2. 协作解决问题
```json
{
  "message": [
    {"role": "system", "content": "You are a collaborative problem solver."},
    {"role": "user", "content": "We need to optimize this algorithm."},
    {"role": "assistant", "content": "Let's analyze it step by step. What's the current complexity?"},
    {"role": "user", "content": "It's O(n²) with nested loops."},
    {"role": "assistant", "content": "We can likely improve that. What data structure are you using?"},
    {"role": "user", "content": "Just arrays. Here's the code..."},
    {"role": "assistant", "content": "I see the issue. We can use a hash map to reduce complexity."},
    {"role": "user", "content": "Show me how to implement that."}
  ]
}
```

### 批量测试脚本

#### Windows 批量测试
```batch
@echo off
echo Testing various list message scenarios...

REM 测试1：编程助手
curl -X POST http://localhost:8001/agent/run -H "Content-Type: application/json" -d "{\"model_name\": \"qwen3:1.7b\", \"mcp_urls\": [\"http://localhost:8000/mcp\"], \"message\": [{\"role\": \"system\", \"content\": \"You are a Python expert.\"}, {\"role\": \"user\", \"content\": \"Explain list comprehensions.\"}]}"

REM 测试2：数学教学
curl -X POST http://localhost:8001/agent/run -H "Content-Type: application/json" -d "{\"model_name\": \"qwen3:1.7b\", \"mcp_urls\": [\"http://localhost:8000/mcp\"], \"message\": [{\"role\": \"system\", \"content\": \"You are a math teacher.\"}, {\"role\": \"user\", \"content\": \"Explain derivatives.\"}]}"

echo All tests completed!
```

#### Linux/macOS 批量测试
```bash
#!/bin/bash
echo "Testing various list message scenarios..."

# 测试1：编程助手
curl -X POST http://localhost:8001/agent/run \
  -H "Content-Type: application/json" \
  -d '{
    "model_name": "qwen3:1.7b",
    "mcp_urls": ["http://localhost:8000/mcp"],
    "message": [
      {"role": "system", "content": "You are a Python expert."},
      {"role": "user", "content": "Explain list comprehensions."}
    ]
  }'

# 测试2：数学教学
curl -X POST http://localhost:8001/agent/run \
  -H "Content-Type: application/json" \
  -d '{
    "model_name": "qwen3:1.7b",
    "mcp_urls": ["http://localhost:8000/mcp"],
    "message": [
      {"role": "system", "content": "You are a math teacher."},
      {"role": "user", "content": "Explain derivatives."}
    ]
  }'

echo "All tests completed!"
```

### 性能优化建议

1. **对话历史管理**：
   - 限制消息列表长度（建议不超过20条）
   - 定期清理无关的历史消息
   - 保留关键上下文信息

2. **Token 优化**：
   - 使用简洁的系统消息
   - 避免重复的内容
   - 根据需要调整 `max_tokens` 参数

3. **缓存策略**：
   - 对相似的对话模式进行缓存
   - 复用成功的系统消息模板
   - 预定义常用的对话流程

## 📝 许可证

本项目采用 MIT 许可证。
