import subprocess
import os
import signal
import time
import psutil

def reboot_mcp():
    """重启add2img.py对应的MCP服务器"""
    try:
        # 查找正在运行的add2img.py进程
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = proc.info['cmdline']
                if cmdline and 'add2img.py' in ' '.join(cmdline):
                    print(f"找到MCP服务器进程 PID: {proc.info['pid']}")
                    # 终止进程
                    proc.terminate()
                    # 等待进程结束
                    proc.wait(timeout=5)
                    print("MCP服务器已停止")
                    break
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
        
        # 等待一下确保端口释放
        time.sleep(2)
        
        # 重新启动MCP服务器
        print("正在重启MCP服务器...")
        subprocess.Popen([
            'python', 'add2img.py'
        ], cwd=os.getcwd())
        
        print("MCP服务器重启完成")
        
    except Exception as e:
        print(f"重启MCP服务器时出错: {e}")

def create_tool(tool):
    description = tool.description
    name = tool.name
    code = tool.code
    import_code = tool.import_code
    with open(f"tools/{name}.py", "w") as f:
        f.write(import_code)
        f.write("\n")
        f.write(code)
        f.write("\n")
        f.write(f"{name}.__doc__ = '{description}'")
    
    # 调用重启MCP服务器
    reboot_mcp()
