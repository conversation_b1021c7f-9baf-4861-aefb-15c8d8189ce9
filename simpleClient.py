import asyncio
from typing import List, Union, Any, Optional, Type
import sys
from pydantic import BaseModel

from agents import (Agent, Runner, gen_trace_id, trace,
    set_default_openai_api,
    set_default_openai_client,
    set_tracing_disabled)
from agents.mcp import MCPServerStreamableHttp
from agents.model_settings import ModelSettings
from openai.types.responses import ResponseTextDeltaEvent

from openai import AsyncOpenAI


from create_tool import create_tool

async def run_agent_with_mcp(
    model_name: str,
    mcp_urls: List[str],
    instructions: str,
    message: Union[str, List[Any]],
    stream: bool = True,
    temperature: float = 0.0,
    max_tokens: int = 1000,
    top_p: float = 1.0,
    frequency_penalty: float = 0.0,
    presence_penalty: float = 0.0,
    base_url: str = "http://localhost:11434/v1",
    api_key: str = "",
    use_tracing: bool = False
) -> Union[str, Any]:
    """
    运行一个带有MCP服务器的Agent
    
    Args:
        model_name: 模型名称
        mcp_urls: MCP服务器URL列表
        instructions: Agent指令
        message: 要处理的消息
        stream: 是否启用流式输出
        temperature: 温度参数 
        max_tokens: 最大token数
        top_p: top_p参数
        frequency_penalty: 频率惩罚
        presence_penalty: 存在惩罚
        base_url: OpenAI API基础URL
        api_key: API密钥
        use_tracing: 是否启用跟踪
        
    Returns:
        str 或 streaming result，取决于stream参数
    """
    
    # 设置OpenAI客户端
    client = AsyncOpenAI(
        base_url=base_url,
        api_key=api_key,
    )
    set_default_openai_client(client=client, use_for_tracing=use_tracing)
    set_default_openai_api("chat_completions")
    set_tracing_disabled(disabled=not use_tracing)
    
    # 创建MCP服务器列表
    mcp_servers = []
    for i, url in enumerate(mcp_urls):
        server = MCPServerStreamableHttp(
            name=f"MCP Server {i+1}",
            params={
                "url": url,
            },
        )
        mcp_servers.append(server)
    
    # 创建ModelSettings
    model_settings = ModelSettings(
        temperature=temperature,
        max_tokens=max_tokens,
        top_p=top_p,
        frequency_penalty=frequency_penalty,
        presence_penalty=presence_penalty,
    )
    
    async def run_with_servers():
        # 正确的异步上下文管理器使用方式
        # 为每个服务器创建独立的上下文管理器
        server_contexts = []
        servers = []
        
        try:
            # 逐个进入每个服务器的上下文
            for server in mcp_servers:
                server_context = server.__aenter__()
                server_instance = await server_context
                server_contexts.append((server, server_instance))
                servers.append(server_instance)
            
            # 创建Agent
            agent = Agent(
                name="Assistant",
                instructions=instructions,
                mcp_servers=servers,
                model=model_name,
                model_settings=model_settings
            )
            
            if stream:
                # 流式处理
                print(f"Running (streaming): {message}")
                result = Runner.run_streamed(
                    starting_agent=agent, 
                    input=message
                )
                
                print("=== Streaming Response ===")
                
                async for event in result.stream_events():
                    # 处理原始响应事件（token级别的流式输出）
                    if event.type == "raw_response_event" and isinstance(event.data, ResponseTextDeltaEvent):
                        print(event.data.delta, end="", flush=True)
                    # 处理运行项目流事件（更高级别的事件）
                    elif event.type == "run_item_stream_event":
                        if event.item.type == "tool_call_item":
                            print("\n-- Tool was called --")
                        elif event.item.type == "tool_call_output_item":
                            print(f"\n-- Tool output: {event.item.output}")
                    # 处理Agent更新事件
                    elif event.type == "agent_updated_stream_event":
                        print(f"\n-- Agent updated: {event.new_agent.name} --")
                
                print("\n=== Streaming Complete ===")
                
                
            else:
                # 非流式处理
                print(f"Running: {message}")
                result = await Runner.run(starting_agent=agent, input=message)
                return result.final_output
                
        finally:
            # 按相反顺序清理服务器连接，确保在同一任务中退出
            for server, _ in reversed(server_contexts):
                try:
                    await server.__aexit__(None, None, None)
                except Exception as e:
                    print(f"Error cleaning up server {server}: {e}")
                    # 继续清理其他服务器，不要因为一个服务器的清理失败而停止
    
    if use_tracing:
        trace_id = gen_trace_id()
        with trace(workflow_name="MCP Agent Run", trace_id=trace_id):
            return await run_with_servers()
    else:
        return await run_with_servers()

async def run_agent_with_structured_output(
    model_name: str,
    instructions: str,
    message: Union[str, List[Any]],
    output_type: Type[BaseModel],
    stream: bool = False,
    temperature: float = 0.0,
    max_tokens: int = 1000,
    top_p: float = 1.0,
    frequency_penalty: float = 0.0,
    presence_penalty: float = 0.0,
    mcp_urls: List[str]=[],
    base_url: str = "http://localhost:11434/v1",
    api_key: str = "",
    use_tracing: bool = False
) -> Union[BaseModel, Any]:
    """
    运行一个带有结构化输出的MCP Agent
    
    Args:
        model_name: 模型名称
        mcp_urls: MCP服务器URL列表
        instructions: Agent指令
        message: 要处理的消息
        output_type: 输出类型，必须是Pydantic BaseModel
        stream: 是否启用流式输出（结构化输出通常不使用流式）
        temperature: 温度参数
        max_tokens: 最大token数
        top_p: top_p参数
        frequency_penalty: 频率惩罚
        presence_penalty: 存在惩罚
        base_url: OpenAI API基础URL
        api_key: API密钥
        use_tracing: 是否启用跟踪
        
    Returns:
        BaseModel实例或streaming result，取决于stream参数
    """
    
    # 设置OpenAI客户端
    client = AsyncOpenAI(
        base_url=base_url,
        api_key=api_key,
    )
    set_default_openai_client(client=client, use_for_tracing=use_tracing)
    set_default_openai_api("chat_completions")
    set_tracing_disabled(disabled=not use_tracing)
    
    # 创建MCP服务器列表
    mcp_servers = []
    for i, url in enumerate(mcp_urls):
        server = MCPServerStreamableHttp(
            name=f"MCP Server {i+1}",
            params={
                "url": url,
            },
        )
        mcp_servers.append(server)
    
    # 创建ModelSettings
    model_settings = ModelSettings(
        temperature=temperature,
        max_tokens=max_tokens,
        top_p=top_p,
        frequency_penalty=frequency_penalty,
        presence_penalty=presence_penalty,
    )
    
    async def run_with_servers():
        # 正确的异步上下文管理器使用方式
        # 为每个服务器创建独立的上下文管理器
        server_contexts = []
        servers = []
        
        try:
            # 逐个进入每个服务器的上下文
            for server in mcp_servers:
                server_context = server.__aenter__()
                server_instance = await server_context
                server_contexts.append((server, server_instance))
                servers.append(server_instance)
            
            # 创建Agent，指定output_type用于结构化输出
            agent = Agent(
                name="Assistant",
                instructions=instructions,
                mcp_servers=servers,
                model=model_name,
                model_settings=model_settings,
                output_type=output_type  # 关键：设置结构化输出类型
            )
            
            if stream:
                # 流式处理（注意：结构化输出通常不适合流式处理）
                print(f"Running (streaming): {message}")
                result = Runner.run_streamed(
                    starting_agent=agent, 
                    input=message
                )
                
                print("=== Streaming Response ===")
                
                async for event in result.stream_events():
                    # 处理原始响应事件（token级别的流式输出）
                    if event.type == "raw_response_event" and isinstance(event.data, ResponseTextDeltaEvent):
                        print(event.data.delta, end="", flush=True)
                    # 处理运行项目流事件（更高级别的事件）
                    elif event.type == "run_item_stream_event":
                        if event.item.type == "tool_call_item":
                            print("\n-- Tool was called --")
                        elif event.item.type == "tool_call_output_item":
                            print(f"\n-- Tool output: {event.item.output}")
                    # 处理Agent更新事件
                    elif event.type == "agent_updated_stream_event":
                        print(f"\n-- Agent updated: {event.new_agent.name} --")
                
                print("\n=== Streaming Complete ===")
                return result
                
            else:
                # 非流式处理 - 推荐用于结构化输出
                print(f"Running: {message}")
                result = await Runner.run(starting_agent=agent, input=message)
                
                # 返回结构化的final_output，它应该是output_type的实例
                return result.final_output
                
        finally:
            # 按相反顺序清理服务器连接，确保在同一任务中退出
            for server, _ in reversed(server_contexts):
                try:
                    await server.__aexit__(None, None, None)
                except Exception as e:
                    print(f"Error cleaning up server {server}: {e}")
                    # 继续清理其他服务器，不要因为一个服务器的清理失败而停止
    
    if use_tracing:
        trace_id = gen_trace_id()
        with trace(workflow_name="MCP Agent Structured Output Run", trace_id=trace_id):
            return await run_with_servers()
    else:
        return await run_with_servers()

async def create_tool_by_message(model_name: str,
    message: Union[str, List[Any]],
    temperature: float = 0.0,
    max_tokens: int = 1000,
    top_p: float = 1.0,
    frequency_penalty: float = 0.0,
    presence_penalty: float = 0.0,
    base_url: str = "http://localhost:11434/v1",
    api_key: str = "",
    use_tracing: bool = False):
    """
    根据消息创建工具
    """
    # 定义结构化输出模型
    class Tool(BaseModel):
        name: str
        description: str
        code: str
        import_code: str
    instructions="""You are a tool creator assistant. write python function to create a tool,
        your output should have four fields: name, description, code, import_code.
        Attention: import library code should be in the import_code field, 
        and the code field should be a python function without import_code.
        all parameters and result must have type annotation.
        example:
        {
            "name": "add",
            "description": "add two numbers",
            "code": "def add(a: int, b: int) -> int:\n return a + b",
            "import_code": "import math\n"
        }
        """
    tool = await run_agent_with_structured_output(
        model_name=model_name,
        instructions=instructions,
        message=message,
        output_type=Tool,
        stream=False,  # 结构化输出通常不使用流式
        temperature=temperature,
        max_tokens=max_tokens,
        top_p=top_p,
        frequency_penalty=frequency_penalty,
        presence_penalty=presence_penalty,
        base_url=base_url,
        api_key=api_key,
        use_tracing=use_tracing
    )
    create_tool(tool)


# 示例用法函数
async def example_usage():
    """示例用法"""
    # # 基本用法
    # result = await run_agent_with_mcp(
    #     model_name="qwen3:4b",
    #     mcp_urls=["http://localhost:11435/mcp"],
    #     instructions="Use the tools to answer the questions.",
    #     message="what's 87 x 123899 x 9877?",
    #     stream=True,
    #     temperature=0.0
    # )
    # print(f"\nFinal result: {result}")
    
    # # 多个MCP服务器的用法
    # result2 = await run_agent_with_mcp(
    #     model_name="qwen3:4b",
    #     mcp_urls=[
    #         "http://localhost:11435/mcp"
    #     ],
    #     instructions="You are a helpful assistant with access to multiple tools.",
    #     message="Add these numbers: 7 and 22.",
    #     stream=False,  # 非流式输出
    #     temperature=0.1,
    #     max_tokens=500
    # )
    # print(f"Result 2: {result2}")
    
    # 结构化输出示例1：工具创建
    


    # 结构化输出示例2：人物信息提取
    # person_result = await run_agent_with_structured_output(
    #     model_name="qwen3:1.7b",
    #     mcp_urls=["http://localhost:11435/mcp"],
    #     instructions="You are an information extraction assistant. Extract person information from the given text and format it according to the specified structure.",
    #     message="John Smith is a 35-year-old software engineer who specializes in Python, JavaScript, and machine learning. He has been working in the tech industry for over 10 years.",
    #     output_type=PersonInfo,
    #     stream=False,
    #     temperature=0.0
    # )
    # print(f"\n=== 结构化输出示例2：人物信息提取 ===")
    # print(f"姓名: {person_result.name}")
    # print(f"年龄: {person_result.age}")
    # print(f"职业: {person_result.occupation}")
    # print(f"技能: {person_result.skills}")
    
    # # 结构化输出示例3：带工具调用的结构化输出
    # class CalculationResult(BaseModel):
    #     question: str
    #     answer: float
    #     tool_used: bool
    #     explanation: str
    
    # calc_result = await run_agent_with_structured_output(
    #     model_name="qwen3:4b",
    #     mcp_urls=["http://localhost:11435/mcp"],
    #     instructions="You are a calculator assistant with access to calculation tools. Use the tools if available to solve math problems and provide structured results.",
    #     message="What is 456 * 789?",
    #     output_type=CalculationResult,
    #     stream=False,
    #     temperature=0.0,
    #     use_tracing=True  # 启用跟踪以便调试
    # )
    # print(f"\n=== 结构化输出示例3：工具调用计算 ===")
    # print(f"问题: {calc_result.question}")
    # print(f"答案: {calc_result.answer}")
    # print(f"使用了工具: {calc_result.tool_used}")
    # print(f"解释: {calc_result.explanation}")


if __name__ == "__main__":
    try:
        asyncio.run(create_tool_by_message(
            model_name="qwen3:4b",
            message="create a tool to add text to image",
            temperature=0.0,
            max_tokens=1000,
        ))
    finally:
        print(f"服务结束")