# 项目介绍

## 功能列表

* [X] 基本的AI聊天功能
* [X] 话题管理
* [X] 助手管理
* [X] 集成MCP功能
* [X] 多用户使用
* [X] 图片渲染
* [X] 文件上传
* [ ] 多智能体编排
* [ ] 数字人

## 项目运转流程

1. 启动4个服务器（顺序不影响，彼此独立，合作完成功能）

   1. 启动chat 服务器(ts编写，提供前端主界面、用户身份验证、收集用户输入并转发给提供AI功能的后端)
   2. 启动MCP服务器(MCP服务器写了一些可供AI调用的工具)
   3. 启动agent服务器(agent服务器调用MCP服务器提供AI功能)
   4. 启动文件服务器（为文件生成内网全局url，使不同服务器的程序都能读取到用户传输的文件）
2. 用户登录，获取access token
3. 当用户输入信息点击发送按钮时

   1. 如果未添加MCP
      1. 发送acces token、模型设置信息、用户信息给服务器
      2. token校验通过后，服务器通过ollama api获取响应，转发给前端
   2. 如果添加了MCP
      1. 发送acces token、模型设置信息、用户信息给服务器
      2. token校验通过后，服务器通过python 服务器获取响应，转发给前端
4. 如果用户上传了图片

   1. 图片会自动上传到文件服务器
   2. 用户上传图片的行为和上传图片的url会自动发送给用户
5. 如果用户希望让AI添加工具

   1. 点击发送行上面的添加工具图标（倒数第二个图标）
   2. 添加工具图标被点亮后，输入添加工具的指令给AI
   3. AI自动添加工具（添加工具详细流程如下：）
      1. AI生成固定模式的tool对象（code,import_code,description,name）
      2. 使用预先编写的create_tool函数解析tool对象，在tools目录下生成新文件
      3. MCP服务器从tools文件夹动态读取所有py文件，并注册为工具
      4. 刷新MCP服务器，使其读取到新的py文件
   4. 添加完成后刷新MCP服务器，会发现工具数量加一，并且在输入框的上一行输入工具处点击MCP工具图标（最后一个图标）可以看到新添加的工具

![1752398601158](image/0713总结/1752398601158.png)

界面截图
