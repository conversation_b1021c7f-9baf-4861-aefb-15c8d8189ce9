#!/usr/bin/env python3
import asyncio
import aiohttp
import json
import sys

async def test_stream_endpoint():
    """测试流式端点"""
    url = "http://localhost:8001/agent/stream"
    
    # 测试数据
    test_data = {
        "model_name": "qwen3:1.7b",
        "mcp_urls": ["http://localhost:8000/mcp"],
        "instructions": "Use the tools to answer the questions.",
        "message": "what's (36 + 51) x 123899?",
        "stream": True,
        "temperature": 0.0,
        "max_tokens": 1000,
        "top_p": 1.0,
        "frequency_penalty": 0.0,
        "presence_penalty": 0.0,
        "base_url": "http://localhost:11434/v1",
        "api_key": "",
        "use_tracing": False
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            print("发送流式请求...")
            async with session.post(url, json=test_data) as response:
                if response.status == 200:
                    print("开始接收流式响应:")
                    print("=" * 50)
                    
                    async for line in response.content:
                        if line:
                            line_str = line.decode('utf-8').strip()
                            if line_str.startswith('data: '):
                                try:
                                    data = json.loads(line_str[6:])  # 去掉 'data: ' 前缀
                                    
                                    if data['type'] == 'start':
                                        print(f"🚀 {data['message']}")
                                    elif data['type'] == 'token':
                                        print(data['content'], end='', flush=True)
                                    elif data['type'] == 'tool_call':
                                        print(f"\n🔧 {data['message']}")
                                    elif data['type'] == 'tool_output':
                                        print(f"\n📋 工具输出: {data['content']}")
                                    elif data['type'] == 'agent_updated':
                                        print(f"\n👤 Agent更新: {data['agent']}")
                                    elif data['type'] == 'complete':
                                        print(f"\n✅ {data['message']}")
                                    elif data['type'] == 'error':
                                        print(f"\n❌ 错误: {data['message']}")
                                        
                                except json.JSONDecodeError:
                                    print(f"无法解析JSON: {line_str}")
                    
                    print("\n" + "=" * 50)
                    print("流式响应完成!")
                else:
                    print(f"请求失败: {response.status}")
                    text = await response.text()
                    print(f"错误信息: {text}")
                    
    except Exception as e:
        print(f"连接错误: {str(e)}")

async def test_non_stream_endpoint():
    """测试非流式端点"""
    url = "http://localhost:8001/agent/run"
    
    # 测试数据
    test_data = {
        "model_name": "qwen3:1.7b",
        "mcp_urls": ["http://localhost:8000/mcp"],
        "instructions": "Use the tools to answer the questions.",
        "message": "Add these numbers: 7 and 22.",
        "stream": False,
        "temperature": 0.1,
        "max_tokens": 500
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            print("发送非流式请求...")
            async with session.post(url, json=test_data) as response:
                if response.status == 200:
                    result = await response.json()
                    print("非流式响应:")
                    print("=" * 50)
                    print(f"结果: {result['result']}")
                    print(f"状态: {result['status']}")
                    print("=" * 50)
                else:
                    print(f"请求失败: {response.status}")
                    text = await response.text()
                    print(f"错误信息: {text}")
                    
    except Exception as e:
        print(f"连接错误: {str(e)}")

async def test_health():
    """测试健康检查"""
    url = "http://localhost:8001/health"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    result = await response.json()
                    print("健康检查通过:")
                    print(f"状态: {result['status']}")
                    print(f"服务: {result['service']}")
                else:
                    print(f"健康检查失败: {response.status}")
                    
    except Exception as e:
        print(f"健康检查连接错误: {str(e)}")

async def main():
    """主函数"""
    print("MCP Agent Web Service 客户端测试")
    print("=" * 50)
    
    # 测试健康检查
    await test_health()
    print()
    
    # 根据命令行参数选择测试类型
    if len(sys.argv) > 1:
        if sys.argv[1] == "stream":
            await test_stream_endpoint()
        elif sys.argv[1] == "nostream":
            await test_non_stream_endpoint()
        else:
            print("使用方法: python test_client.py [stream|nostream]")
    else:
        # 默认测试流式
        await test_stream_endpoint()

if __name__ == "__main__":
    asyncio.run(main()) 