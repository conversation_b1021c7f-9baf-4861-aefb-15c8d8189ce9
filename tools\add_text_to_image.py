from PIL import Image, ImageDraw, ImageFont

from PIL import Image, ImageDraw, ImageFont

def add_text_to_image(image_path: str, output_path: str, text: str, font_size: int = 24) -> None:
    image = Image.open(image_path)
    draw = ImageDraw.Draw(image)
    font = ImageFont.truetype("arial.ttf", font_size)
    draw.text((10, 10), text, font=font, fill=(0, 0, 0))
    image.save(output_path)

add_text_to_image.__doc__ = 'Add text to an image'