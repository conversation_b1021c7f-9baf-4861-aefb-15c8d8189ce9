import OpenAI from 'openai';
import { OpenAIChatCompletionsModel, setDefaultOpenAIClient,hostedMcpTool,ModelResponse} from '@openai/agents';
import { aisdk } from '@openai/agents-extensions';
import { ollama } from 'ollama-ai-provider';



const model = aisdk(ollama("qwen3:1.7b"));


// Setup OpenAI client
const myclient = new OpenAI({
  baseURL: "http://localhost:11434/v1",
  apiKey: "sk-proj-123",
});
setDefaultOpenAIClient(myclient);
import { Agent, setTracingDisabled, run } from '@openai/agents';

setTracingDisabled(true);


interface RunAgentOptions {
  modelName: string;
  mcpUrls: string[];
  instructions: string;
  message: string;
  stream?: boolean;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  baseUrl?: string;
  apiKey?: string;
  useTracing?: boolean;
}

interface StreamEvent {
  type: string;
  data?: any;
  item?: any;
  newAgent?: any;
}

/**
 * Run an Agent with MCP servers
 * 
 * @param options Configuration options for running the agent
 * @returns Promise<string> or streaming result, depending on stream parameter
 */
export async function runAgentWithMcp(options: RunAgentOptions): Promise<string | any> {
  const {
    modelName,
    mcpUrls,
    instructions,
    message,
    stream = true,
    temperature = 0.0,
    maxTokens = 1000,
    topP = 1.0,
    frequencyPenalty = 0.0,
    presencePenalty = 0.0,
    baseUrl = "http://localhost:11434/v1",
    apiKey = "123",
    useTracing = false
  } = options;






  // Create model settings
  const modelSettings = {
    temperature,
    maxTokens,
    topP,
    frequencyPenalty,
    presencePenalty,
  };

  try {
    // Create Agent with MCP servers as tools
    // Setup OpenAI client
    const myclient = new OpenAI({
      baseURL: "http://localhost:11434/v1",
      apiKey: "sk-proj-123",
    });
    setDefaultOpenAIClient(myclient);

    const agent = new Agent({
      name: "Assistant",
      instructions,
      tools: [hostedMcpTool({
        serverLabel: 'Demo 🚀',
        serverUrl: 'http://localhost:8000/mcp',
      })], // MCP servers can be used as tools
      modelSettings,
      model: "qwen3:4b",
      
    });

    if (stream) {
      // Streaming execution using the run utility function
      console.log(`Running (streaming): ${message}`);
      
      const streams = await run(agent, message, { 
        stream: true
      });
      
      console.log("=== Streaming Response ===");

      // // Process streaming events
      // for await (const chunk of streams) {
      //   // Handle streaming text output
      //   // if (typeof chunk === 'string') {
      //   //   process.stdout.write(chunk);
      //   // } else if (chunk && typeof chunk === 'object') {
      //   //   // Handle other event types
      //   //   if (chunk.type === 'tool_call') {
      //   //     console.log("\n-- Tool was called --");
      //   //   } else if (chunk.type === 'tool_output') {
      //   //     console.log(`\n-- Tool output: ${chunk.output}`);
      //   //   } else if (chunk.type === 'agent_update') {
      //   //     console.log(`\n-- Agent updated: ${chunk.agent?.name} --`);
      //   //   }
      //   // }
      // }

      console.log("\n=== Streaming Complete ===");
      
      // The final result is typically the last chunk or needs to be collected
      return "Streaming completed"; // Placeholder return value

    } else {
      // Non-streaming execution
      console.log(`Running: ${message}`);
      
      const result = await run(agent, message);
      
      // return result.finalOutput;
    }

  } catch (error) {
    console.error('Error running agent:', error);
    throw error;
  } finally {
  
  }
}

// Example usage functions
export async function exampleUsage(): Promise<void> {
  try {
    // Basic usage
    const result = await runAgentWithMcp({
      modelName: "qwen3:4b",
      mcpUrls: ["http://localhost:8000/mcp"],
      instructions: "Use the tools to answer the questions.",
      message: "what's (36 + 51) x 123899?",
      stream: false,
      temperature: 0.0
    });
    // console.log(`\nFinal result: ${result}`);

    // // Multiple MCP servers usage
    // const result2 = await runAgentWithMcp({
    //   modelName: "qwen3:1.7b",
    //   mcpUrls: [
    //     "http://localhost:8000/mcp"
    //   ],
    //   instructions: "You are a helpful assistant with access to multiple tools.",
    //   message: "Add these numbers: 7 and 22.",
    //   stream: false, // Non-streaming output
    //   temperature: 0.1,
    //   maxTokens: 500
    // });
    // console.log(`Result 2: ${result2}`);

  } catch (error) {
    console.error('Error in example usage:', error);
  }
}

// Main execution
async function main(): Promise<void> {
  try {
    await exampleUsage();
  } finally {
    console.log("Service ended");
  }
}

// Run the main function
main().catch(console.error); 