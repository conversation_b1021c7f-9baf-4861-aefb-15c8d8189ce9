[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "paper-search-mcp"
version = "0.1.3"
authors = [
  { name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>" },
]
description = "A MCP server for searching and downloading academic papers from multiple sources."
readme = "README.md"
requires-python = ">=3.10"

dependencies = [
    "requests",
    "feedparser",
    "fastmcp",
    "mcp[cli]>=1.6.0",
    "PyPDF2>=3.0.0",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0", # Better HTML parser for BeautifulSoup
    "httpx[socks]>=0.28.1",
]

[tool.hatch.build.targets.wheel]
packages = ["paper_search_mcp"]
